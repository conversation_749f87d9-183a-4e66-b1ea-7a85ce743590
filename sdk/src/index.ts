import { FlakinessReport } from '@flakiness/flakiness-report';
import chalk from 'chalk';
import fs from 'fs';
import open from "open";
import path from 'path';
import { FlakinessProjectConfig } from "./flakinessProjectConfig.js";
import { LocalReportServer } from './localReportServer.js';
import { ExternalAttachment } from './reportUploader.js';
// Re-exports
export { FlakinessReport } from '@flakiness/flakiness-report';
export { createEnvironment } from './createEnvironment.js';
export { createTestStepSnippetsInplace } from './createTestStepSnippets.js';
export { FlakinessProjectConfig } from './flakinessProjectConfig.js';
export { computeGitRoot, gitCommitInfo } from './git.js';
export * as pathutils from './pathutils.js';
export { createDataAttachment, createFileAttachment, ExternalAttachment, ReportUploader } from './reportUploader.js';
export { ReportUtils } from './reportUtils.js';
export { SystemUtilizationSampler } from './systemUtilizationSampler.js';
export { stripAnsi } from './utils.js';

// This function tries to search for well-known env variables to figure out run URL.
export function inferRunUrl() {
  if (process.env.GITHUB_REPOSITORY && process.env.GITHUB_RUN_ID)
    return `https://github.com/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`;
  return undefined;
}

export async function showReport(reportFolder: string) {
  const reportPath = path.join(reportFolder, 'report.json');

  const config = await FlakinessProjectConfig.load();
  const projectPublicId = config.projectPublicId();

  const reportViewerEndpoint = config.reportViewerEndpoint();
  const server = await LocalReportServer.create({
    endpoint: reportViewerEndpoint,
    port: 9373,
    reportPath,
    attachmentsFolder: reportFolder,
  });

  const url = new URL(reportViewerEndpoint);
  url.searchParams.set('port', String(server.port()));
  url.searchParams.set('token', server.authToken());
  if (projectPublicId)
    url.searchParams.set('ppid', projectPublicId);

  console.log(chalk.cyan(`
  Serving Flakiness report at ${(url.toString())}
  Press Ctrl+C to quit.`))
  await open(url.toString());
  await new Promise(() => {});
}

export async function saveReport(report: FlakinessReport.Report, attachments: ExternalAttachment[], outputFolder: string): Promise<ExternalAttachment[]> {
  // Write report and its attachments to outputFolder.
  const reportPath = path.join(outputFolder, 'report.json');
  const attachmentsFolder = path.join(outputFolder, 'attachments');
  await fs.promises.rm(outputFolder, { recursive: true, force: true });
  await fs.promises.mkdir(outputFolder, { recursive: true });
  await fs.promises.writeFile(reportPath, JSON.stringify(report), 'utf-8');

  if (attachments.length)
    await fs.promises.mkdir(attachmentsFolder);

  const movedAttachments: ExternalAttachment[] = [];
  for (const attachment of attachments) {
    const attachmentPath = path.join(attachmentsFolder, attachment.id);
    if (attachment.path)
      await fs.promises.cp(attachment.path, attachmentPath);
    else if (attachment.body)
      await fs.promises.writeFile(attachmentPath, attachment.body);
    movedAttachments.push({
      contentType: attachment.contentType,
      id: attachment.id,
      path: attachmentPath,
    });
  }
  return movedAttachments;
}