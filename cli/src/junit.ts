/**
 * This is largely based upon a nice writeup from here: https://github.com/testmoapp/junitxml
 */
import { ExternalAttachment, FlakinessReport as FK, ReportUtils } from '@flakiness/sdk';
import { parseXml, XmlElement, XmlNode, XmlText } from '@rgrove/parse-xml';
import assert from 'assert';
import fs from 'fs';
import path from 'path';
import { sha1Buffer, sha1File } from './utils.js';

type ProcessingContext = {
  report: FK.Report,
  attachments: Map<FK.AttachmentId, ExternalAttachment>,
  currentSuite?: FK.Suite,
  currentEnv: FK.Environment,
  currentEnvIndex: number,
  currentTimeMs: number,
  ignoreAttachments: boolean,
}

function getProperties(element: XmlElement): [string, string][] {
  const propertiesNodes = element.children.filter(node => node instanceof XmlElement).filter(node => node.name === 'properties');
  if (!propertiesNodes.length)
    return [];
  const result: [string, string][] = [];
  for (const propertiesNode of propertiesNodes) {
    const properties = propertiesNode.children.filter(node => node instanceof XmlElement).filter(node => node.name === 'property');
    for (const property of properties) {
      const name = property.attributes['name'];
      const innerText = property.children.find(node => node instanceof XmlText);
      const value = property.attributes['value'] ?? innerText?.text ?? '';
      result.push([name, value]);
    }
  }
  return result;
}

function extractErrors(testcase: XmlElement): FK.ReportError[]|undefined {
  const xmlErrors = testcase.children
    .filter(e => e instanceof XmlElement)
    .filter(element => element.name === 'error' || element.name === 'failure');
  if (!xmlErrors.length)
    return undefined;

  const errors: FK.ReportError[] = [];
  for (const xmlErr of xmlErrors) {
    const message = [xmlErr.attributes['type'], xmlErr.attributes['message']].filter(x => !!x).join(' ');
    const xmlStackNodes = xmlErr.children.filter(child => child instanceof XmlText);
    const stack = xmlStackNodes ? xmlStackNodes.map(node => node.text).join('\n') : undefined;
    errors.push({
      message,
      stack,
    });
  }
  return errors;
}

function extractStdout(testcase: XmlElement, stdio: 'system-out'|'system-err'): FK.STDIOEntry[]|undefined {
  const xmlStdio = testcase.children
    .filter(e => e instanceof XmlElement)
    .filter(element => element.name === stdio);
  if (!xmlStdio.length)
    return undefined;
  return xmlStdio.map(node => node.children.filter(node => node instanceof XmlText)).flat().map(txtNode => ({
    text: txtNode.text,
  }));
}

async function parseAttachment(value: string): Promise<ExternalAttachment> {
  // There are 3 types of attachments: files, data URLs, and just some values.
  // Check if the value points to a local file
  let absolutePath = path.resolve(process.cwd(), value);
  if (fs.existsSync(absolutePath)) {
    const id = await sha1File(absolutePath) as FK.AttachmentId;
    return {
      contentType: 'image/png',
      path: absolutePath, 
      id,
    }
  }

  //TODO: handle URLs and data URLs as well.
  return {
    contentType: 'text/plain',
    id: sha1Buffer(value) as FK.AttachmentId,
    body: Buffer.from(value),
  }
}

async function traverseJUnitReport(context: ProcessingContext, node: XmlNode) {
  const element = node;
  if (!(element instanceof XmlElement))
    return;

  let { currentEnv, currentEnvIndex, currentSuite, report, currentTimeMs, attachments } = context;

  // If the node has a "timestamp" attribute, then this is our new current time.
  if (element.attributes['timestamp'])
    currentTimeMs = new Date(element.attributes['timestamp']).getTime();

  if (element.name === 'testsuite') {
    // Create a new suite for the testSuite node.
    const file = element.attributes['file'];
    const line = parseInt(element.attributes['line'], 10);
    const name = element.attributes['name'];
    const newSuite: FK.Suite = {
      title: name ?? file,
      location: file && !isNaN(line) ? {
        file: file as FK.GitFilePath,
        line: line as FK.Number1Based,
        column: 1 as FK.Number1Based,
      } : undefined,
      type: name ? 'suite' : 
            file ? 'file' :
            'anonymous suite',
      suites: [],
      tests: [],
    }
    if (currentSuite) {
      currentSuite.suites ??= [];
      currentSuite.suites.push(newSuite);
    } else {
      report.suites.push(newSuite);
    }
    currentSuite = newSuite;

    // If testsuite defines some property nodes, than treat these
    // as user-supplied data for the environments. 
    const userSuppliedData = getProperties(element);
    if (userSuppliedData.length) {
      currentEnv = structuredClone(currentEnv);
      currentEnv.userSuppliedData ??= {};
      for (const [key, value] of userSuppliedData)
        currentEnv.userSuppliedData[key] = value;
      currentEnvIndex = report.environments.push(currentEnv) - 1;
    }
  } else if (element.name === 'testcase') {
    // If there's no 
    assert(currentSuite);
    const file = element.attributes['file'];
    const name = element.attributes['name'];
    const line = parseInt(element.attributes['line'], 10);
    const timeMs = parseFloat(element.attributes['time']) * 1000;

    const startTimestamp = currentTimeMs as FK.UnixTimestampMS;
    const duration = timeMs as FK.DurationMS;
    currentTimeMs += timeMs;

    const annotations: FK.Annotation[] = [];
    const attachments: FK.Attachment[] = [];
    for (const [key, value] of getProperties(element)) {
      // JUnit attachments start with "attachment" key
      if (key.toLowerCase().startsWith('attachment')) {
        if (context.ignoreAttachments)
          continue;

        const attachment = await parseAttachment(value);
        context.attachments.set(attachment.id, attachment);
        attachments.push({
          id: attachment.id,
          contentType: attachment.contentType,
          //TODO: better default names for attachments?
          name: attachment.path ? path.basename(attachment.path) : `attachment`,
        });

      } else {
        annotations.push({
          type: key,
          description: value.length ? value : undefined,
        });
      }
    }

    const childElements = element.children.filter(child => child instanceof XmlElement);
    const xmlSkippedAnnotation = childElements.find(child => child.name === 'skipped');
    if (xmlSkippedAnnotation)
      annotations.push({ type: 'skipped', description: xmlSkippedAnnotation.attributes['message'] });

    const expectedStatus: FK.TestStatus = xmlSkippedAnnotation ? 'skipped' : 'passed';

    const errors = extractErrors(element);
    const test: FK.Test = {
      title: name,
      location: file && !isNaN(line) ? {
        file: file as FK.GitFilePath,
        line: line as FK.Number1Based,
        column: 1 as FK.Number1Based,
      } : undefined,
      attempts: [{
        environmentIdx: currentEnvIndex,
        expectedStatus: expectedStatus,
        annotations,
        attachments,
        startTimestamp,
        duration,
        status: xmlSkippedAnnotation ? 'skipped' : errors ? 'failed' : 'passed',
        errors,
        stdout: extractStdout(element, 'system-out'),
        stderr: extractStdout(element, 'system-err'),
      }]
    };
    currentSuite.tests ??= [];
    currentSuite.tests.push(test);
  }

  context = { ...context, currentEnv, currentEnvIndex, currentSuite, currentTimeMs };
  for (const child of element.children)
    await traverseJUnitReport(context, child);
}

export async function parseJUnit(xmls: string[], options: {
  defaultEnv: FK.Environment,
  commitId: FK.CommitId,
  runDuration: FK.DurationMS,
  runStartTimestamp: FK.UnixTimestampMS,
  runUrl?: string,
  ignoreAttachments?: boolean,
}): Promise<{ report: FK.Report, attachments: ExternalAttachment[] }>  {
  const report: FK.Report = {
    category: 'junit',
    commitId: options.commitId,
    duration: options.runDuration,
    startTimestamp: options.runStartTimestamp,
    url: options.runUrl,
    environments: [options.defaultEnv],
    suites: [],
    unattributedErrors: [],
  };

  const context: ProcessingContext = {
    currentEnv: options.defaultEnv,
    currentEnvIndex: 0,
    currentTimeMs: 0,
    report,
    currentSuite: undefined,
    attachments: new Map(),
    ignoreAttachments: !!options.ignoreAttachments,
  };

  for (const xml of xmls) {
    const doc = parseXml(xml);
    for (const element of doc.children)
      await traverseJUnitReport(context, element);
  }
  return {
    report: ReportUtils.dedupeSuitesTestsEnvironments(report),
    attachments: Array.from(context.attachments.values()),
  };
}
